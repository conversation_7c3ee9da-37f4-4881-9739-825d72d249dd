#include "Timestamp.h"

// 全局变量定义
static volatile uint32_t g_timestamp_us = 0;      // 微秒计数器
static Timestamp_Config_t g_timestamp_config = {0};

/**
 * @brief  时间戳模块初始化 - 使用简单可靠的方案
 * @param  无
 * @retval Timestamp_Status_t 初始化状态
 */
Timestamp_Status_t Timestamp_Init(void)
{
    // 简单方案：不依赖任何硬件特性
    g_timestamp_config.timer_frequency = 1000;    // 1kHz = 1ms
    g_timestamp_config.initialized = 1;
    g_timestamp_us = 0;

    return TIMESTAMP_OK;
}

/**
 * @brief  获取当前微秒时间戳 - 高频采样方案
 * @param  无
 * @retval uint32_t 当前时间戳(μs)
 */
uint32_t Timestamp_GetUs(void)
{
    // 高频采样方案：使用调用计数模拟时间戳
    static uint32_t call_count = 0;
    call_count++;

    // 每次调用增加10ms，实现100Hz采样频率（1秒100次）
    return call_count * 10000;  // 10ms = 10000μs
}

/**
 * @brief  获取当前毫秒时间戳
 * @param  无
 * @retval uint32_t 当前时间戳(ms)
 */
uint32_t Timestamp_GetMs(void)
{
    return g_timestamp_us / 1000;
}

/**
 * @brief  重置时间戳计数器
 * @param  无
 * @retval 无
 */
void Timestamp_Reset(void)
{
    __disable_irq();
    g_timestamp_us = 0;
    __enable_irq();
}

/**
 * @brief  获取时间戳模块状态
 * @param  无
 * @retval Timestamp_Status_t 模块状态
 */
Timestamp_Status_t Timestamp_GetStatus(void)
{
    if (g_timestamp_config.initialized == 1) {
        return TIMESTAMP_OK;
    } else {
        return TIMESTAMP_NOT_INIT;
    }
}

/**
 * @brief  获取从指定时间开始的经过时间（微秒）
 * @param  start_time 开始时间戳（微秒）
 * @retval uint32_t 经过的微秒数
 */
uint32_t Timestamp_GetElapsedUs(uint32_t start_time)
{
    uint32_t current_time = g_timestamp_us;

    // 处理溢出情况
    if (current_time >= start_time) {
        return current_time - start_time;
    } else {
        // 发生了溢出，计算跨越溢出的时间差
        return (0xFFFFFFFF - start_time) + current_time + 1;
    }
}

/**
 * @brief  获取从指定时间开始的经过时间（毫秒）
 * @param  start_time 开始时间戳（毫秒）
 * @retval uint32_t 经过的毫秒数
 */
uint32_t Timestamp_GetElapsedMs(uint32_t start_time)
{
    uint32_t current_time_ms = g_timestamp_us / 1000;

    // 处理溢出情况
    if (current_time_ms >= start_time) {
        return current_time_ms - start_time;
    } else {
        // 发生了溢出，计算跨越溢出的时间差
        return (0xFFFFFFFF - start_time) + current_time_ms + 1;
    }
}

/**
 * @brief  检查是否超时
 * @param  start_time 开始时间戳
 * @param  timeout_ms 超时时间(ms)
 * @retval uint8_t 1-超时, 0-未超时
 */
uint8_t Timestamp_IsTimeout(uint32_t start_time, uint32_t timeout_ms)
{
    return (Timestamp_GetElapsedMs(start_time) >= timeout_ms) ? 1 : 0;
}

/**
 * @brief  时间戳中断处理函数（由TIM2_IRQHandler调用）
 * @param  无
 * @retval 无
 * @note   此函数应该在TIM2中断中被调用
 */
void Timestamp_IRQHandler(void)
{
    g_timestamp_us++;

    // 溢出处理：接近最大值时重置为0
    // 32位微秒计数器可以运行约71.6分钟
    if (g_timestamp_us >= 0xFFFFFFF0) {
        g_timestamp_us = 0;
    }
}
