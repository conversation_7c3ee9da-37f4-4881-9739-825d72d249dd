#include "Timestamp.h"

// 全局变量定义
static volatile uint32_t g_timestamp_ms = 0;
static Timestamp_Config_t g_timestamp_config = {0};

/**
 * @brief  时间戳模块初始化
 * @param  无
 * @retval Timestamp_Status_t 初始化状态
 */
Timestamp_Status_t Timestamp_Init(void)
{
    // 配置参数
    g_timestamp_config.tick_frequency = 1000;  // 1kHz = 1ms
    g_timestamp_config.priority = 3;           // 较低优先级
    
    // 配置SysTick定时器：72MHz / 72000 = 1kHz = 1ms
    if (SysTick_Config(SystemCoreClock / g_timestamp_config.tick_frequency) != 0) {
        return TIMESTAMP_ERROR;
    }
    
    // 设置中断优先级
    NVIC_SetPriority(SysTick_IRQn, g_timestamp_config.priority);
    
    // 初始化计数器
    g_timestamp_ms = 0;
    g_timestamp_config.initialized = 1;
    
    return TIMESTAMP_OK;
}

/**
 * @brief  获取当前毫秒时间戳
 * @param  无
 * @retval uint32_t 当前时间戳(ms)
 */
uint32_t Timestamp_GetMs(void)
{
    return g_timestamp_ms;
}

/**
 * @brief  重置时间戳计数器
 * @param  无
 * @retval 无
 */
void Timestamp_Reset(void)
{
    __disable_irq();
    g_timestamp_ms = 0;
    __enable_irq();
}

/**
 * @brief  获取时间戳模块状态
 * @param  无
 * @retval Timestamp_Status_t 模块状态
 */
Timestamp_Status_t Timestamp_GetStatus(void)
{
    if (g_timestamp_config.initialized == 1) {
        return TIMESTAMP_OK;
    } else {
        return TIMESTAMP_NOT_INIT;
    }
}

/**
 * @brief  获取从指定时间开始的经过时间
 * @param  start_time 开始时间戳
 * @retval uint32_t 经过的毫秒数
 */
uint32_t Timestamp_GetElapsedMs(uint32_t start_time)
{
    uint32_t current_time = g_timestamp_ms;
    
    // 处理溢出情况
    if (current_time >= start_time) {
        return current_time - start_time;
    } else {
        // 发生了溢出，计算跨越溢出的时间差
        return (0xFFFFFFFF - start_time) + current_time + 1;
    }
}

/**
 * @brief  检查是否超时
 * @param  start_time 开始时间戳
 * @param  timeout_ms 超时时间(ms)
 * @retval uint8_t 1-超时, 0-未超时
 */
uint8_t Timestamp_IsTimeout(uint32_t start_time, uint32_t timeout_ms)
{
    return (Timestamp_GetElapsedMs(start_time) >= timeout_ms) ? 1 : 0;
}

/**
 * @brief  时间戳中断处理函数（由SysTick_Handler调用）
 * @param  无
 * @retval 无
 * @note   此函数应该在SysTick中断中被调用
 */
void Timestamp_IRQHandler(void)
{
    g_timestamp_ms++;

    // 溢出处理：接近最大值时重置为0
    if (g_timestamp_ms >= 0xFFFFFFF0) {
        g_timestamp_ms = 0;
    }
}
