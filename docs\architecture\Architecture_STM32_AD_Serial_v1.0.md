# STM32 AD值串口传输优化 - 系统架构设计文档

## 1. 文档信息

| 项目信息 | 详情 |
|---------|------|
| **项目名称** | STM32 AD值串口传输优化 |
| **架构版本** | v1.0 |
| **创建日期** | 2025-09-03 |
| **架构师** | Bob |
| **文档状态** | 初版 |

## 2. 架构概览

### 2.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    STM32F103C8 系统架构                      │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                  │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   Main Loop     │  │   Data Format   │                   │
│  │   - AD采集      │  │   - 时间戳格式化 │                   │
│  │   - 数据发送    │  │   - 数据串行化   │                   │
│  │   - OLED显示    │  │   - 错误处理     │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (Hardware Abstraction Layer)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ Timestamp   │ │    USART    │ │     ADC     │            │
│  │ - SysTick   │ │ - 串口发送  │ │ - AD转换    │            │
│  │ - 毫秒计数  │ │ - 格式输出  │ │ - 数据读取  │            │
│  │ - 时间获取  │ │ - 状态检查  │ │             │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  系统服务层 (System Services)                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │   SysTick   │ │    NVIC     │ │    RCC      │            │
│  │   中断服务  │ │   中断控制  │ │   时钟管理  │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  硬件层 (Hardware Layer)                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │  SysTick    │ │   USART1    │ │    ADC1     │            │
│  │   定时器    │ │   串口硬件  │ │   ADC硬件   │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 数据流图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   SysTick   │───▶│ Timestamp   │───▶│   Format    │
│   1ms中断   │    │   Counter   │    │   Module    │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
┌─────────────┐    ┌─────────────┐           ▼
│    ADC1     │───▶│  AD_Value   │    ┌─────────────┐
│   PA0输入   │    │   读取      │───▶│ Data Merge  │
└─────────────┘    └─────────────┘    │ & Serialize │
                                      └─────────────┘
                                              │
┌─────────────┐    ┌─────────────┐           ▼
│   USART1    │◀───│ Serial Send │    ┌─────────────┐
│   PA9输出   │    │   Module    │◀───│ Output Data │
└─────────────┘    └─────────────┘    │[TS],[AD]\r\n│
                                      └─────────────┘
```

## 3. 核心技术决策

### 3.1 时间戳实现方案

#### 3.1.1 技术选型分析
| 方案 | 优点 | 缺点 | 选择 |
|------|------|------|------|
| SysTick中断 | 精度高、系统集成度好 | 需要中断处理 | ✅ 选择 |
| 定时器TIM2 | 独立性好、功能丰富 | 占用额外资源 | ❌ |
| 软件计数 | 简单易实现 | 精度差、易漂移 | ❌ |

#### 3.1.2 SysTick配置策略
- **时钟源**：HCLK (72MHz)
- **分频设置**：72000分频，实现1ms中断
- **计数器**：32位无符号整数，支持49.7天连续运行
- **中断优先级**：设置为较低优先级，避免影响关键系统功能

#### 3.1.3 与现有Delay模块的兼容性
**现状分析**：
- 当前Delay.c使用SysTick作为阻塞延时
- 每次调用都重新配置SysTick
- 延时结束后关闭SysTick

**兼容性策略**：
- 保持Delay模块现有接口不变
- 新增独立的时间戳模块
- 使用不同的SysTick配置方式
- 通过条件编译实现共存

### 3.2 模块设计架构

#### 3.2.1 Timestamp模块设计
```c
// Timestamp.h - 接口定义
typedef struct {
    volatile uint32_t milliseconds;    // 毫秒计数器
    uint8_t initialized;               // 初始化标志
} Timestamp_t;

// 核心接口
void Timestamp_Init(void);             // 初始化时间戳模块
uint32_t Timestamp_GetMs(void);       // 获取当前毫秒时间戳
void Timestamp_Reset(void);           // 重置时间戳计数器
```

#### 3.2.2 数据格式化模块设计
```c
// DataFormat.h - 数据格式化接口
typedef enum {
    DATA_FORMAT_OK = 0,
    DATA_FORMAT_ERROR = 1
} DataFormat_Status_t;

// 核心接口
DataFormat_Status_t DataFormat_ADWithTimestamp(
    uint32_t timestamp, 
    uint16_t adValue, 
    char* buffer, 
    uint16_t bufferSize
);
```

#### 3.2.3 串口扩展模块设计
```c
// usart.h - 扩展接口
void Serial_SendTimestampData(uint32_t timestamp, uint16_t adValue);
void Serial_SendErrorData(uint32_t timestamp);
uint8_t Serial_IsTxReady(void);        // 检查发送就绪状态
```

## 4. 关键技术实现方案

### 4.1 SysTick中断处理方案

#### 4.1.1 中断服务函数设计
```c
// 全局时间戳变量
volatile uint32_t g_timestamp_ms = 0;

// SysTick中断服务函数
void SysTick_Handler(void)
{
    g_timestamp_ms++;                  // 毫秒计数递增
    
    // 溢出处理：自动重置为0
    if (g_timestamp_ms == 0xFFFFFFFF) {
        g_timestamp_ms = 0;
    }
}
```

#### 4.1.2 初始化配置
```c
void Timestamp_Init(void)
{
    // 配置SysTick为1ms中断
    SysTick_Config(SystemCoreClock / 1000);
    
    // 设置中断优先级（较低优先级）
    NVIC_SetPriority(SysTick_IRQn, 3);
    
    // 重置计数器
    g_timestamp_ms = 0;
}
```

### 4.2 数据格式化算法

#### 4.2.1 格式化函数实现
```c
DataFormat_Status_t DataFormat_ADWithTimestamp(
    uint32_t timestamp, 
    uint16_t adValue, 
    char* buffer, 
    uint16_t bufferSize)
{
    // 安全检查
    if (buffer == NULL || bufferSize < 20) {
        return DATA_FORMAT_ERROR;
    }
    
    // 格式化数据：[时间戳],[AD值]\r\n
    int len = sprintf(buffer, "%lu,%u\r\n", timestamp, adValue);
    
    return (len > 0) ? DATA_FORMAT_OK : DATA_FORMAT_ERROR;
}
```

#### 4.2.2 错误处理格式化
```c
DataFormat_Status_t DataFormat_ErrorWithTimestamp(
    uint32_t timestamp, 
    char* buffer, 
    uint16_t bufferSize)
{
    if (buffer == NULL || bufferSize < 15) {
        return DATA_FORMAT_ERROR;
    }
    
    int len = sprintf(buffer, "%lu,ERROR\r\n", timestamp);
    return (len > 0) ? DATA_FORMAT_OK : DATA_FORMAT_ERROR;
}
```

### 4.3 串口发送优化方案

#### 4.3.1 非阻塞发送实现
```c
void Serial_SendTimestampData(uint32_t timestamp, uint16_t adValue)
{
    char buffer[32];
    
    // 格式化数据
    if (DataFormat_ADWithTimestamp(timestamp, adValue, buffer, sizeof(buffer)) == DATA_FORMAT_OK) {
        // 发送格式化后的字符串
        Serial_SendString(buffer);
    }
}

uint8_t Serial_IsTxReady(void)
{
    // 检查USART发送数据寄存器是否为空
    return (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == SET) ? 1 : 0;
}
```

## 5. 性能分析与优化

### 5.1 系统资源占用分析

#### 5.1.1 内存占用评估
| 模块 | RAM占用 | Flash占用 | 说明 |
|------|---------|-----------|------|
| 时间戳模块 | 8 bytes | ~200 bytes | 全局变量+函数代码 |
| 格式化模块 | 32 bytes | ~300 bytes | 缓冲区+格式化函数 |
| 串口扩展 | 0 bytes | ~150 bytes | 仅函数代码 |
| **总计** | **40 bytes** | **~650 bytes** | 占用率 < 5% |

#### 5.1.2 CPU占用分析
- **SysTick中断**：每1ms执行1次，耗时 < 1μs
- **数据格式化**：每100ms执行1次，耗时 < 10μs  
- **串口发送**：每100ms执行1次，耗时 < 50μs
- **总CPU占用率** < 0.1%

### 5.2 时序性能分析

#### 5.2.1 关键时序指标
- **时间戳精度**：±1ms
- **数据采集间隔**：100ms ±1ms
- **串口发送延迟**：< 5ms
- **系统响应时间**：< 1ms

#### 5.2.2 性能优化策略
1. **中断优化**：SysTick中断函数保持最简
2. **缓冲优化**：使用栈缓冲区，避免动态分配
3. **算法优化**：使用高效的数值转换算法
4. **发送优化**：检查发送状态，避免阻塞

## 6. 错误处理与容错设计

### 6.1 异常情况处理

#### 6.1.1 时间戳溢出处理
- **检测机制**：在中断中检测溢出
- **处理策略**：自动重置为0，继续计数
- **影响评估**：仅影响时间连续性，不影响功能

#### 6.1.2 ADC转换失败处理
- **检测机制**：检查ADC转换完成标志
- **处理策略**：发送错误标识数据
- **错误格式**：`[timestamp],ERROR\r\n`

#### 6.1.3 串口发送异常处理
- **检测机制**：检查USART发送状态
- **处理策略**：重试机制，最大重试3次
- **超时保护**：单次发送超时10ms

### 6.2 系统稳定性保证

#### 6.2.1 中断安全设计
- 使用volatile关键字保护共享变量
- 中断函数保持简洁，避免复杂操作
- 合理设置中断优先级，避免冲突

#### 6.2.2 内存安全设计
- 所有缓冲区操作进行边界检查
- 使用栈变量，避免内存泄漏
- 字符串操作使用安全函数

## 7. 集成与兼容性设计

### 7.1 与现有系统的集成

#### 7.1.1 模块依赖关系
```
Timestamp Module
    ├── 依赖：SysTick, NVIC
    └── 被依赖：DataFormat, Main

DataFormat Module  
    ├── 依赖：Timestamp, stdio
    └── 被依赖：USART, Main

USART Extension
    ├── 依赖：DataFormat, 原USART模块
    └── 被依赖：Main
```

#### 7.1.2 现有功能保持
- **OLED显示**：完全保持现有功能
- **AD采集**：保持现有采集逻辑
- **延时功能**：通过条件编译保持兼容
- **串口基础功能**：保持向后兼容

### 7.2 可扩展性设计

#### 7.2.1 接口扩展性
- 时间戳模块支持微秒级扩展
- 数据格式支持多种输出格式
- 串口模块支持多通道扩展

#### 7.2.2 功能扩展性
- 支持数据缓存功能扩展
- 支持数据压缩功能扩展
- 支持多传感器数据融合

## 8. 部署与测试策略

### 8.1 分阶段部署计划

#### 8.1.1 第一阶段：基础模块
1. 实现Timestamp模块
2. 验证时间戳精度和稳定性
3. 确保与现有系统兼容

#### 8.1.2 第二阶段：数据处理
1. 实现DataFormat模块
2. 集成时间戳和AD数据
3. 验证数据格式正确性

#### 8.1.3 第三阶段：系统集成
1. 集成所有模块到主程序
2. 完整功能测试
3. 性能和稳定性验证

### 8.2 测试验证方案

#### 8.2.1 单元测试
- 时间戳精度测试
- 数据格式化正确性测试
- 串口发送功能测试

#### 8.2.2 集成测试
- 端到端数据流测试
- 长时间稳定性测试
- 异常情况处理测试

#### 8.2.3 性能测试
- CPU占用率测试
- 内存使用测试
- 实时性能测试

---

**架构设计完成时间**：2025-09-03  
**下一步**：开始详细的模块实现开发
