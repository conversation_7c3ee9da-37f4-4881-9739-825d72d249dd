#include "DataFormat.h"
#include <stdio.h>
#include <string.h>

/**
 * @brief  格式化AD数据
 * @param  timestamp 时间戳
 * @param  adValue AD值
 * @param  buffer 输出缓冲区
 * @param  bufferSize 缓冲区大小
 * @retval DataFormat_Status_t 格式化状态
 */
DataFormat_Status_t DataFormat_ADData(
    uint32_t timestamp, 
    uint16_t adValue, 
    char* buffer, 
    uint16_t bufferSize)
{
    // 参数检查
    if (buffer == NULL) {
        return FORMAT_INVALID_PARAM;
    }
    
    if (bufferSize < FORMAT_BUFFER_SIZE) {
        return FORMAT_BUFFER_TOO_SMALL;
    }
    
    // 格式化数据：[时间戳],[AD值]\r\n
    int len = snprintf(buffer, bufferSize, "%lu,%u\r\n", timestamp, adValue);
    
    if (len <= 0 || len >= bufferSize) {
        return FORMAT_ERROR;
    }
    
    return FORMAT_OK;
}

/**
 * @brief  格式化错误数据
 * @param  timestamp 时间戳
 * @param  buffer 输出缓冲区
 * @param  bufferSize 缓冲区大小
 * @retval DataFormat_Status_t 格式化状态
 */
DataFormat_Status_t DataFormat_ErrorData(
    uint32_t timestamp, 
    char* buffer, 
    uint16_t bufferSize)
{
    if (buffer == NULL) {
        return FORMAT_INVALID_PARAM;
    }
    
    if (bufferSize < 20) {  // "4294967295,ERROR\r\n" = 19字符
        return FORMAT_BUFFER_TOO_SMALL;
    }
    
    int len = snprintf(buffer, bufferSize, "%lu,ERROR\r\n", timestamp);
    
    if (len <= 0 || len >= bufferSize) {
        return FORMAT_ERROR;
    }
    
    return FORMAT_OK;
}

/**
 * @brief  获取所需缓冲区大小
 * @param  type 数据类型
 * @retval uint16_t 所需缓冲区大小
 */
uint16_t DataFormat_GetRequiredBufferSize(DataFormat_Type_t type)
{
    switch (type) {
        case DATA_TYPE_NORMAL:
            return FORMAT_BUFFER_SIZE;      // "4294967295,4095\r\n" = 18字符
        case DATA_TYPE_ERROR:
            return 20;                      // "4294967295,ERROR\r\n" = 19字符
        default:
            return FORMAT_BUFFER_SIZE;
    }
}

/**
 * @brief  验证缓冲区有效性
 * @param  buffer 缓冲区指针
 * @param  size 缓冲区大小
 * @retval uint8_t 1-有效, 0-无效
 */
uint8_t DataFormat_ValidateBuffer(char* buffer, uint16_t size)
{
    if (buffer == NULL || size < 16) {
        return 0;
    }
    return 1;
}
