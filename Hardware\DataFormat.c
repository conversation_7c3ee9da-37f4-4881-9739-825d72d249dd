#include "DataFormat.h"
#include <string.h>

// 自定义数字转字符串函数
static void uint32_to_string(uint32_t num, char* str)
{
    char temp[12];  // 最大支持32位数字
    int i = 0, j = 0;

    // 处理0的特殊情况
    if (num == 0) {
        str[0] = '0';
        str[1] = '\0';
        return;
    }

    // 转换数字到临时字符串（逆序）
    while (num > 0) {
        temp[i++] = '0' + (num % 10);
        num /= 10;
    }

    // 反转字符串
    while (i > 0) {
        str[j++] = temp[--i];
    }
    str[j] = '\0';
}

static void uint16_to_string(uint16_t num, char* str)
{
    uint32_to_string((uint32_t)num, str);
}

/**
 * @brief  格式化AD数据
 * @param  timestamp 时间戳
 * @param  adValue AD值
 * @param  buffer 输出缓冲区
 * @param  bufferSize 缓冲区大小
 * @retval DataFormat_Status_t 格式化状态
 */
DataFormat_Status_t DataFormat_ADData(
    uint32_t timestamp, 
    uint16_t adValue, 
    char* buffer, 
    uint16_t bufferSize)
{
    // 参数检查
    if (buffer == NULL) {
        return FORMAT_INVALID_PARAM;
    }
    
    if (bufferSize < FORMAT_BUFFER_SIZE) {
        return FORMAT_BUFFER_TOO_SMALL;
    }
    
    // 格式化数据：[时间戳],[AD值]\r\n
    char timestamp_str[12];  // 最大32位数字
    char advalue_str[6];     // 最大16位数字

    // 转换数字为字符串
    uint32_to_string(timestamp, timestamp_str);
    uint16_to_string(adValue, advalue_str);

    // 手动构建格式化字符串
    int pos = 0;
    int i;

    // 复制时间戳
    for (i = 0; timestamp_str[i] != '\0' && pos < bufferSize - 1; i++) {
        buffer[pos++] = timestamp_str[i];
    }

    // 添加逗号
    if (pos < bufferSize - 1) buffer[pos++] = ',';

    // 复制AD值
    for (i = 0; advalue_str[i] != '\0' && pos < bufferSize - 1; i++) {
        buffer[pos++] = advalue_str[i];
    }

    // 添加\r\n
    if (pos < bufferSize - 2) {
        buffer[pos++] = '\r';
        buffer[pos++] = '\n';
    }

    // 添加字符串结束符
    if (pos < bufferSize) {
        buffer[pos] = '\0';
    } else {
        return FORMAT_BUFFER_TOO_SMALL;
    }

    return FORMAT_OK;
}

/**
 * @brief  格式化错误数据
 * @param  timestamp 时间戳
 * @param  buffer 输出缓冲区
 * @param  bufferSize 缓冲区大小
 * @retval DataFormat_Status_t 格式化状态
 */
DataFormat_Status_t DataFormat_ErrorData(
    uint32_t timestamp, 
    char* buffer, 
    uint16_t bufferSize)
{
    if (buffer == NULL) {
        return FORMAT_INVALID_PARAM;
    }
    
    if (bufferSize < 20) {  // "4294967295,ERROR\r\n" = 19字符
        return FORMAT_BUFFER_TOO_SMALL;
    }
    
    // 手动构建错误格式字符串
    char timestamp_str[12];  // 最大32位数字
    uint32_to_string(timestamp, timestamp_str);

    int pos = 0;
    int i;

    // 复制时间戳
    for (i = 0; timestamp_str[i] != '\0' && pos < bufferSize - 1; i++) {
        buffer[pos++] = timestamp_str[i];
    }

    // 添加",ERROR\r\n"
    const char* error_suffix = ",ERROR\r\n";
    for (i = 0; error_suffix[i] != '\0' && pos < bufferSize - 1; i++) {
        buffer[pos++] = error_suffix[i];
    }

    // 添加字符串结束符
    if (pos < bufferSize) {
        buffer[pos] = '\0';
    } else {
        return FORMAT_BUFFER_TOO_SMALL;
    }

    return FORMAT_OK;
}

/**
 * @brief  获取所需缓冲区大小
 * @param  type 数据类型
 * @retval uint16_t 所需缓冲区大小
 */
uint16_t DataFormat_GetRequiredBufferSize(DataFormat_Type_t type)
{
    switch (type) {
        case DATA_TYPE_NORMAL:
            return FORMAT_BUFFER_SIZE;      // "4294967295,4095\r\n" = 18字符
        case DATA_TYPE_ERROR:
            return 20;                      // "4294967295,ERROR\r\n" = 19字符
        default:
            return FORMAT_BUFFER_SIZE;
    }
}

/**
 * @brief  验证缓冲区有效性
 * @param  buffer 缓冲区指针
 * @param  size 缓冲区大小
 * @retval uint8_t 1-有效, 0-无效
 */
uint8_t DataFormat_ValidateBuffer(char* buffer, uint16_t size)
{
    if (buffer == NULL || size < 16) {
        return 0;
    }
    return 1;
}
