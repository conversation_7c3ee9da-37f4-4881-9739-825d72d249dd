#include "DataFormat.h"
#include <string.h>

// 优化的数字转字符串函数 - 减少临时变量使用
static int uint32_to_string_fast(uint32_t num, char* str)
{
    int len = 0;
    uint32_t temp = num;

    // 计算数字长度
    if (num == 0) {
        str[0] = '0';
        str[1] = '\0';
        return 1;
    }

    // 计算位数
    while (temp > 0) {
        len++;
        temp /= 10;
    }

    // 从后往前填充
    str[len] = '\0';
    for (int i = len - 1; i >= 0; i--) {
        str[i] = '0' + (num % 10);
        num /= 10;
    }

    return len;
}

static int uint16_to_string_fast(uint16_t num, char* str)
{
    return uint32_to_string_fast((uint32_t)num, str);
}

/**
 * @brief  格式化AD数据
 * @param  timestamp 时间戳
 * @param  adValue AD值
 * @param  buffer 输出缓冲区
 * @param  bufferSize 缓冲区大小
 * @retval DataFormat_Status_t 格式化状态
 */
DataFormat_Status_t DataFormat_ADData(
    uint32_t timestamp, 
    uint16_t adValue, 
    char* buffer, 
    uint16_t bufferSize)
{
    // 参数检查
    if (buffer == NULL) {
        return FORMAT_INVALID_PARAM;
    }
    
    if (bufferSize < FORMAT_BUFFER_SIZE) {
        return FORMAT_BUFFER_TOO_SMALL;
    }
    
    // 优化的格式化数据：[时间戳],[AD值]\r\n
    int pos = 0;

    // 直接转换时间戳到缓冲区
    pos += uint32_to_string_fast(timestamp, &buffer[pos]);

    // 检查缓冲区空间
    if (pos >= bufferSize - 10) return FORMAT_BUFFER_TOO_SMALL;

    // 添加逗号
    buffer[pos++] = ',';

    // 直接转换AD值到缓冲区
    pos += uint16_to_string_fast(adValue, &buffer[pos]);

    // 检查缓冲区空间
    if (pos >= bufferSize - 3) return FORMAT_BUFFER_TOO_SMALL;

    // 添加\r\n\0
    buffer[pos++] = '\r';
    buffer[pos++] = '\n';
    buffer[pos] = '\0';

    return FORMAT_OK;
}

/**
 * @brief  格式化错误数据
 * @param  timestamp 时间戳
 * @param  buffer 输出缓冲区
 * @param  bufferSize 缓冲区大小
 * @retval DataFormat_Status_t 格式化状态
 */
DataFormat_Status_t DataFormat_ErrorData(
    uint32_t timestamp, 
    char* buffer, 
    uint16_t bufferSize)
{
    if (buffer == NULL) {
        return FORMAT_INVALID_PARAM;
    }
    
    if (bufferSize < 20) {  // "4294967295,ERROR\r\n" = 19字符
        return FORMAT_BUFFER_TOO_SMALL;
    }
    
    // 优化的错误格式字符串构建
    int pos = 0;

    // 直接转换时间戳到缓冲区
    pos += uint32_to_string_fast(timestamp, &buffer[pos]);

    // 检查缓冲区空间
    if (pos >= bufferSize - 10) return FORMAT_BUFFER_TOO_SMALL;

    // 添加",ERROR\r\n\0"
    const char* error_suffix = ",ERROR\r\n";
    int i = 0;
    while (error_suffix[i] != '\0' && pos < bufferSize - 1) {
        buffer[pos++] = error_suffix[i++];
    }
    buffer[pos] = '\0';

    return FORMAT_OK;
}

/**
 * @brief  获取所需缓冲区大小
 * @param  type 数据类型
 * @retval uint16_t 所需缓冲区大小
 */
uint16_t DataFormat_GetRequiredBufferSize(DataFormat_Type_t type)
{
    switch (type) {
        case DATA_TYPE_NORMAL:
            return FORMAT_BUFFER_SIZE;      // "4294967295,4095\r\n" = 18字符
        case DATA_TYPE_ERROR:
            return 20;                      // "4294967295,ERROR\r\n" = 19字符
        default:
            return FORMAT_BUFFER_SIZE;
    }
}

/**
 * @brief  验证缓冲区有效性
 * @param  buffer 缓冲区指针
 * @param  size 缓冲区大小
 * @retval uint8_t 1-有效, 0-无效
 */
uint8_t DataFormat_ValidateBuffer(char* buffer, uint16_t size)
{
    if (buffer == NULL || size < 16) {
        return 0;
    }
    return 1;
}
