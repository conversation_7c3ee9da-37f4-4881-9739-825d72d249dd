#ifndef __TIMESTAMP_H
#define __TIMESTAMP_H

#include "stm32f10x.h"

// 时间戳状态定义
typedef enum {
    TIMESTAMP_OK = 0,
    TIMESTAMP_ERROR = 1,
    TIMESTAMP_NOT_INIT = 2
} Timestamp_Status_t;

// 时间戳配置结构
typedef struct {
    uint32_t tick_frequency;    // 中断频率 (Hz)
    uint8_t priority;          // 中断优先级
    uint8_t initialized;       // 初始化标志
} Timestamp_Config_t;

// 核心接口函数
Timestamp_Status_t Timestamp_Init(void);
uint32_t Timestamp_GetMs(void);
void Timestamp_Reset(void);
Timestamp_Status_t Timestamp_GetStatus(void);

// 高级接口函数
uint32_t Timestamp_GetElapsedMs(uint32_t start_time);
uint8_t Timestamp_IsTimeout(uint32_t start_time, uint32_t timeout_ms);

#endif
