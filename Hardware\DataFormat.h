#ifndef __DATAFORMAT_H
#define __DATAFORMAT_H

#include "stm32f10x.h"
#include "Timestamp.h"

// 数据格式化状态
typedef enum {
    FORMAT_OK = 0,
    FORMAT_ERROR = 1,
    FORMAT_BUFFER_TOO_SMALL = 2,
    FORMAT_INVALID_PARAM = 3
} DataFormat_Status_t;

// 数据类型定义
typedef enum {
    DATA_TYPE_NORMAL = 0,
    DATA_TYPE_ERROR = 1
} DataFormat_Type_t;

// 格式化配置
#define FORMAT_BUFFER_SIZE      32
#define FORMAT_TIMESTAMP_MAX    10  // 最大时间戳位数
#define FORMAT_ADVALUE_MAX      4   // 最大AD值位数

// 核心接口函数
DataFormat_Status_t DataFormat_ADData(
    uint32_t timestamp, 
    uint16_t adValue, 
    char* buffer, 
    uint16_t bufferSize
);

DataFormat_Status_t DataFormat_ErrorData(
    uint32_t timestamp, 
    char* buffer, 
    uint16_t bufferSize
);

// 辅助函数
uint16_t DataFormat_GetRequiredBufferSize(DataFormat_Type_t type);
uint8_t DataFormat_ValidateBuffer(char* buffer, uint16_t size);

#endif
