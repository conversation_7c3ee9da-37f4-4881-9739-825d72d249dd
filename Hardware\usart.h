#ifndef __USART_H
#define __USART_H

#include <stdio.h>
#include "DataFormat.h"
#include "Timestamp.h"

// 串口发送状态
typedef enum {
    SERIAL_TX_OK = 0,
    SERIAL_TX_BUSY = 1,
    SERIAL_TX_ERROR = 2,
    SERIAL_TX_TIMEOUT = 3
} Serial_TxStatus_t;

// 原有函数声明
void Serial_Init(void);
void Serial_SendByte(uint8_t Byte);
void Serial_SendArray(uint8_t *Array, uint16_t Length);
void Serial_SendString(char *String);
void Serial_SendNumber(uint32_t Number, uint8_t Length);
void Serial_Printf(char *format, ...);

// 新增函数声明
void Serial_SendTimestampData(uint32_t timestamp, uint16_t adValue);
void Serial_SendErrorData(uint32_t timestamp);
Serial_TxStatus_t Serial_GetTxStatus(void);
uint8_t Serial_IsTxReady(void);
Serial_TxStatus_t Serial_WaitTxComplete(uint32_t timeout_ms);

#endif
