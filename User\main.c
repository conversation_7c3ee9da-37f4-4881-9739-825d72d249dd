#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "AD.h"
#include "usart.h"
#include "Timestamp.h"      // 新增：时间戳模块
#include "DataFormat.h"     // 新增：数据格式化模块

uint16_t ADValue;           // 定义AD值变量
float Voltage;              // 定义电压变量
uint32_t lastTimestamp;     // 新增：上次时间戳
uint8_t adcErrorCount;      // 新增：ADC错误计数
uint8_t displayCounter;     // 新增：显示计数器，用于降低OLED更新频率

int main(void)
{
	/*模块初始化*/
	OLED_Init();			//OLED初始化
	AD_Init();				//AD初始化
	Serial_Init();			//串口初始化

	// 新增：时间戳模块初始化
	if (Timestamp_Init() != TIMESTAMP_OK) {
		// 初始化失败处理 - 在OLED上显示错误信息
		OLED_ShowString(1, 1, "TS Init Error!");
		while(1);  // 停止运行
	}

	/*显示静态字符串*/
	OLED_ShowString(1, 1, "ADValue:");
	OLED_ShowString(2, 1, "Voltage:0.00V");

	// 初始化变量
	lastTimestamp = 0;
	adcErrorCount = 0;
	displayCounter = 0;

	while (1)
	{
		// 获取当前时间戳
		uint32_t currentTimestamp = Timestamp_GetMs();

		// 获取AD转换的值
		ADValue = AD_GetValue();

		// 检查AD值有效性（ADC是12位，最大值4095）
		if (ADValue <= 4095) {
			// 正常数据处理
			Voltage = (float)ADValue / 4095 * 3.3;		//将AD值线性变换到0~3.3的范围，表示电压

			// 发送带时间戳的数据到串口
			Serial_SendTimestampData(currentTimestamp, ADValue);

			// 重置错误计数
			adcErrorCount = 0;
		} else {
			// ADC错误处理
			adcErrorCount++;
			if (adcErrorCount >= 3) {
				// 连续3次错误后发送错误数据
				Serial_SendErrorData(currentTimestamp);
				adcErrorCount = 0;  // 重置计数
			}
		}

		// 优化OLED显示频率：每5次采样更新一次显示（降低到10Hz）
		displayCounter++;
		if (displayCounter >= 5) {
			OLED_ShowNum(1, 9, ADValue, 4);				//显示AD值
			OLED_ShowNum(2, 9, Voltage, 1);				//显示电压值的整数部分
			OLED_ShowNum(2, 11, (uint16_t)(Voltage * 100) % 100, 2);	//显示电压值的小数部分
			displayCounter = 0;  // 重置计数器
		}

		// 延时20ms，实现50Hz采样频率（2秒100次采样）
		Delay_ms(20);

		// 更新上次时间戳
		lastTimestamp = currentTimestamp;
	}
}
